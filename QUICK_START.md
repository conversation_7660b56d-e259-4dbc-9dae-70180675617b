# Suna - Quick Start Guide

Get <PERSON><PERSON> running locally in 5 minutes!

## Prerequisites

- <PERSON>er & Docker Compose installed
- Google OAuth credentials (see setup below)

## Setup Steps

### 1. Clone Repository
```bash
git clone https://github.com/kortix-ai/suna.git
cd suna
```

### 2. Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create/select project → Enable Google+ API
3. Create OAuth 2.0 credentials:
   - **Type**: Web application
   - **Authorized origins**: `http://localhost:3000`
   - **Redirect URIs**: `https://jmmmzhnqkgvibxrhpncy.supabase.co/auth/v1/callback`
4. Copy Client ID and Secret

### 3. Configure Environment
```bash
# Frontend
echo "NEXT_PUBLIC_GOOGLE_CLIENT_ID=YOUR_CLIENT_ID" >> frontend/.env.local

# Backend  
echo "SUPABASE_AUTH_EXTERNAL_GOOGLE_CLIENT_ID=YOUR_CLIENT_ID" >> backend/.env
echo "SUPABASE_AUTH_EXTERNAL_GOOGLE_SECRET=YOUR_CLIENT_SECRET" >> backend/.env
```

### 4. Start Services
```bash
docker compose up -d
```

### 5. Access Application
- **Frontend**: http://localhost:3000
- **Backend**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs

## Testing

1. Open http://localhost:3000
2. Click "Continue with Google"
3. Sign in with your Google account
4. Access dashboard and test AI agent

## Common Commands

```bash
# View logs
docker logs suna-frontend-1 -f
docker logs suna-backend-1 -f

# Restart services
docker compose restart frontend
docker compose restart backend

# Stop all
docker compose down

# Full reset
docker compose down -v && docker compose up -d --build
```

## Troubleshooting

**Google Sign-In issues**: Check `NEXT_PUBLIC_GOOGLE_CLIENT_ID` in `frontend/.env.local`

**Connection errors**: Verify all containers running with `docker ps`

**Port conflicts**: Check ports 3000 and 8000 are available

## Need Help?

- Check the full [README.md](./README.md) for detailed instructions
- Join our [Discord](https://discord.gg/Py6pCBUUPw) for support
- Open an [issue](https://github.com/kortix-ai/suna/issues) for bugs

---

🎉 **You're ready to use Suna!** Start a conversation and let the AI agent help you with your tasks.
