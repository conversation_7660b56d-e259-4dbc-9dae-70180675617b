#!/usr/bin/env python3
"""
Toggle authentication for local development.

This script allows you to easily enable or disable authentication
when running Suna locally for development purposes.

Usage:
    python toggle-auth.py on    # Enable authentication
    python toggle-auth.py off   # Disable authentication
    python toggle-auth.py       # Show current status
"""

import os
import sys
from pathlib import Path

def update_env_file(file_path: str, key: str, value: str):
    """Update or add a key-value pair in an environment file."""
    path = Path(file_path)
    if not path.exists():
        print(f"Warning: {file_path} does not exist")
        return
    
    lines = path.read_text().splitlines()
    updated = False
    
    for i, line in enumerate(lines):
        if line.startswith(f"{key}="):
            lines[i] = f"{key}={value}"
            updated = True
            break
    
    if not updated:
        lines.append(f"{key}={value}")
    
    path.write_text('\n'.join(lines) + '\n')

def get_env_value(file_path: str, key: str) -> str:
    """Get the value of a key from an environment file."""
    path = Path(file_path)
    if not path.exists():
        return "not found"
    
    for line in path.read_text().splitlines():
        if line.startswith(f"{key}="):
            return line.split('=', 1)[1]
    
    return "not set"

def main():
    if len(sys.argv) > 2:
        print("Usage: python toggle-auth.py [on|off]")
        sys.exit(1)
    
    action = sys.argv[1] if len(sys.argv) > 1 else "status"
    
    if action not in ["on", "off", "status"]:
        print("Usage: python toggle-auth.py [on|off|status]")
        sys.exit(1)
    
    backend_env = "backend/.env"
    frontend_env = "frontend/.env.local"
    
    if action == "status":
        backend_auth = get_env_value(backend_env, "SKIP_AUTH")
        frontend_auth = get_env_value(frontend_env, "NEXT_PUBLIC_SKIP_AUTH")
        
        print("Current authentication status:")
        print(f"  Backend SKIP_AUTH: {backend_auth}")
        print(f"  Frontend NEXT_PUBLIC_SKIP_AUTH: {frontend_auth}")
        
        if backend_auth == "true" and frontend_auth == "true":
            print("\n✅ Authentication is DISABLED for local development")
        elif backend_auth == "false" and frontend_auth == "false":
            print("\n🔒 Authentication is ENABLED")
        else:
            print("\n⚠️  Authentication settings are inconsistent")
    
    elif action == "off":
        print("Enabling authentication...")
        update_env_file(backend_env, "SKIP_AUTH", "false")
        update_env_file(frontend_env, "NEXT_PUBLIC_SKIP_AUTH", "false")
        print("✅ Authentication enabled. You'll need to sign in to access the app.")
    
    elif action == "on":
        print("Disabling authentication for local development...")
        update_env_file(backend_env, "SKIP_AUTH", "true")
        update_env_file(frontend_env, "NEXT_PUBLIC_SKIP_AUTH", "true")
        print("✅ Authentication disabled. You can now access the app without signing in.")
        print("⚠️  Remember to restart your development servers for changes to take effect.")

if __name__ == "__main__":
    main() 