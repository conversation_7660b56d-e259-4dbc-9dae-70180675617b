# Google OAuth Setup for Suna

## Quick Setup Instructions

### 1. Create Google OAuth Credentials

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API:
   - Navigate to "APIs & Services" > "Library"
   - Search for "Google+ API" and enable it
4. Create OAuth 2.0 credentials:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application"
   - Set the name: "Suna Local Development"
   - Add authorized JavaScript origins: `http://localhost:3000`
   - Add authorized redirect URIs: `https://jmmmzhnqkgvibxrhpncy.supabase.co/auth/v1/callback`

### 2. Update Environment Variables

After creating your Google OAuth credentials, you'll get:
- **Client ID**: Something like `123456789-abcdef.apps.googleusercontent.com`
- **Client Secret**: Something like `GOCSPX-abcdef123456`

Update these files with your actual credentials:

#### Frontend (.env.local):
```bash
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-actual-client-id.apps.googleusercontent.com
```

#### Backend (.env):
```bash
SUPABASE_AUTH_EXTERNAL_GOOGLE_CLIENT_ID=your-actual-client-id.apps.googleusercontent.com
SUPABASE_AUTH_EXTERNAL_GOOGLE_SECRET=your-actual-client-secret
```

### 3. Restart Services

After updating the environment variables:

```bash
docker compose down
docker compose up -d
```

### 4. Test Google Auth

1. Open http://localhost:3000
2. Click on "Sign in with Google"
3. Complete the OAuth flow
4. You should be redirected to the dashboard

## Troubleshooting

### Common Issues:

1. **"Google Sign-In Not Configured"**: 
   - Make sure `NEXT_PUBLIC_GOOGLE_CLIENT_ID` is set in frontend/.env.local

2. **"Invalid client"**: 
   - Check that your Client ID is correct
   - Ensure `http://localhost:3000` is in authorized origins

3. **"Redirect URI mismatch"**: 
   - Make sure the redirect URI is exactly: `https://jmmmzhnqkgvibxrhpncy.supabase.co/auth/v1/callback`

4. **"Access blocked"**: 
   - Your app needs to be verified by Google for production use
   - For development, add your email as a test user in Google Cloud Console

### Test Users for Development:

While your app is in development mode, add test users:
1. Go to Google Cloud Console > "APIs & Services" > "OAuth consent screen"
2. Scroll down to "Test users"
3. Add your email address

## Current Status

✅ Supabase configuration updated to enable Google OAuth
✅ Environment files prepared with placeholder values
✅ Frontend configured to use Google Sign-In component

🔄 **Next step**: Replace placeholder values with your actual Google OAuth credentials
